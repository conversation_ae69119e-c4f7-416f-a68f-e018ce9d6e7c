<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Connection Test - Feedback Widget</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 40px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .test-section {
            margin-bottom: 32px;
            padding: 24px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .test-btn {
            padding: 10px 16px;
            margin: 5px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .test-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .test-btn.primary {
            background: #00C2A8;
            color: white;
            border-color: #00C2A8;
        }
        
        .test-btn.primary:hover {
            background: #00a58e;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: 500;
        }
        
        .status.success {
            background: #f0fdf4;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .status.info {
            background: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #bfdbfe;
        }
        
        .status.warning {
            background: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }
        
        .test-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 16px;
        }
        
        .env-info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            padding: 16px;
            border-radius: 8px;
            margin-top: 16px;
        }
        
        .env-info h4 {
            color: #0369a1;
            margin: 0 0 8px 0;
        }
        
        .env-info code {
            background: #e0f2fe;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #d1d5db;
            border-top: 2px solid #00C2A8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Backend Connection Test</h1>
        <p>Diagnose and test the connection between your widget and backend.</p>
        
        <div class="test-section">
            <h3>Environment Detection</h3>
            <div id="env-info" class="env-info">
                <h4>Current Environment:</h4>
                <p><strong>Hostname:</strong> <code id="hostname"></code></p>
                <p><strong>Protocol:</strong> <code id="protocol"></code></p>
                <p><strong>API URL:</strong> <code id="api-url"></code></p>
                <p><strong>Environment:</strong> <code id="environment"></code></p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Backend Health Check</h3>
            <p>Test if your backend is running and accessible:</p>
            
            <button class="test-btn primary" onclick="testBackendHealth()">🏥 Test Backend Health</button>
            <button class="test-btn" onclick="testCORS()">🌐 Test CORS</button>
            
            <div id="health-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Submission Test</h3>
            <p>Test actual form submission to backend:</p>
            
            <button class="test-btn primary" onclick="testSubmission()">📤 Test Submission</button>
            <button class="test-btn" onclick="testWithWidget()">🎯 Test with Widget</button>
            
            <div id="submission-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Common Issues & Solutions</h3>
            <div class="status info">
                <strong>If you're getting errors, check:</strong><br>
                • <strong>Local Development:</strong> Is your backend running on <code>http://localhost:5000</code>?<br>
                • <strong>Production:</strong> Is <code>https://user-comments.onrender.com</code> accessible?<br>
                • <strong>CORS:</strong> Does your backend allow cross-origin requests?<br>
                • <strong>Database:</strong> Is your Supabase database connected and accessible?<br>
                • <strong>Network:</strong> Check browser console for detailed error messages
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Log</h3>
            <button class="test-btn" onclick="clearLog()">🗑️ Clear Log</button>
            <div id="test-log" class="test-log"></div>
        </div>
    </div>

    <!-- Load the feedback widget -->
    <script src="widget.js"></script>
    <script>
        // Initialize the widget
        FeedbackWidget.init({
            position: 'bottom-right',
            primaryColor: '#00C2A8',
            title: 'Connection Test',
            placeholder: 'Test backend connection...',
            showName: true,
            showEmail: true
        });

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[BACKEND-TEST] ${message}`);
        }

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        // Detect environment and API URL
        function detectEnvironment() {
            const hostname = window.location.hostname;
            const protocol = window.location.protocol;
            
            const isLocal = hostname === 'localhost' ||
                           hostname === '127.0.0.1' ||
                           hostname === '' ||
                           protocol === 'file:';

            const apiUrl = isLocal ?
                'http://localhost:5000/submit_comment' :
                'https://user-comments.onrender.com/submit_comment';

            const environment = isLocal ? 'LOCAL DEVELOPMENT' : 'PRODUCTION';

            // Update UI
            document.getElementById('hostname').textContent = hostname || 'file://';
            document.getElementById('protocol').textContent = protocol;
            document.getElementById('api-url').textContent = apiUrl;
            document.getElementById('environment').textContent = environment;

            log(`Environment detected: ${environment}`);
            log(`API URL: ${apiUrl}`);

            return { hostname, protocol, apiUrl, environment, isLocal };
        }

        async function testBackendHealth() {
            const { apiUrl, isLocal } = detectEnvironment();
            
            log('Testing backend health...');
            showResult('health-result', '<div class="loading"></div>Testing backend connection...', 'info');

            try {
                // Test root endpoint first
                const rootUrl = apiUrl.replace('/submit_comment', '');
                
                log(`Testing root endpoint: ${rootUrl}`);
                
                const response = await fetch(rootUrl, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Backend is running: ${JSON.stringify(data)}`);
                    showResult('health-result', 
                        `✅ Backend is running successfully!<br>` +
                        `Status: ${response.status}<br>` +
                        `Response: ${JSON.stringify(data)}`,
                        'success'
                    );
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                log(`❌ Backend health check failed: ${error.message}`);
                
                let errorMessage = `❌ Backend connection failed!<br>Error: ${error.message}<br><br>`;
                
                if (isLocal) {
                    errorMessage += `<strong>Local Development Issues:</strong><br>` +
                        `• Make sure your backend is running: <code>python backend/app.py</code><br>` +
                        `• Check if port 5000 is available<br>` +
                        `• Verify backend is accessible at <code>http://localhost:5000</code>`;
                } else {
                    errorMessage += `<strong>Production Issues:</strong><br>` +
                        `• Check if <code>https://user-comments.onrender.com</code> is accessible<br>` +
                        `• Verify your Render deployment is active<br>` +
                        `• Check Render logs for errors`;
                }
                
                showResult('health-result', errorMessage, 'error');
            }
        }

        async function testCORS() {
            const { apiUrl } = detectEnvironment();
            
            log('Testing CORS configuration...');
            showResult('health-result', '<div class="loading"></div>Testing CORS...', 'info');

            try {
                const response = await fetch(apiUrl, {
                    method: 'OPTIONS',
                    mode: 'cors',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });

                log(`CORS preflight response: ${response.status}`);
                
                if (response.ok || response.status === 200) {
                    showResult('health-result', '✅ CORS is properly configured!', 'success');
                } else {
                    showResult('health-result', 
                        `⚠️ CORS might have issues<br>` +
                        `Preflight status: ${response.status}<br>` +
                        `This might still work for simple requests`,
                        'warning'
                    );
                }
            } catch (error) {
                log(`❌ CORS test failed: ${error.message}`);
                showResult('health-result', 
                    `❌ CORS test failed: ${error.message}<br>` +
                    `This could indicate CORS configuration issues`,
                    'error'
                );
            }
        }

        async function testSubmission() {
            const { apiUrl } = detectEnvironment();
            
            log('Testing form submission...');
            showResult('submission-result', '<div class="loading"></div>Testing submission...', 'info');

            const testData = {
                comment: 'Test submission from backend connection test',
                name: 'Test User',
                email: '<EMAIL>',
                company: window.location.hostname || 'test-domain.com',
                website_url: window.location.origin || 'http://test-domain.com',
                user_agent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                widget_version: '1.0.0'
            };

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-Widget-Version': '1.0.0',
                        'X-Widget-Origin': window.location.hostname || 'test'
                    },
                    body: JSON.stringify(testData),
                    mode: 'cors',
                    credentials: 'omit'
                });

                if (response.ok) {
                    const result = await response.json();
                    log(`✅ Submission successful: ${JSON.stringify(result)}`);
                    showResult('submission-result', 
                        `✅ Submission successful!<br>` +
                        `Status: ${response.status}<br>` +
                        `Response: ${JSON.stringify(result)}<br>` +
                        `Your widget should work correctly!`,
                        'success'
                    );
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                log(`❌ Submission failed: ${error.message}`);
                showResult('submission-result', 
                    `❌ Submission failed!<br>` +
                    `Error: ${error.message}<br>` +
                    `Check backend logs and database connection`,
                    'error'
                );
            }
        }

        function testWithWidget() {
            log('Testing with actual widget...');
            showResult('submission-result', 'Opening widget for manual test...', 'info');
            
            FeedbackWidget.open();
            
            setTimeout(() => {
                const commentField = document.getElementById('feedback-widget-comment');
                const nameField = document.getElementById('feedback-widget-name');
                const emailField = document.getElementById('feedback-widget-email');
                
                if (commentField) commentField.value = 'Backend connection test via widget';
                if (nameField) nameField.value = 'Test User';
                if (emailField) emailField.value = '<EMAIL>';
                
                log('Widget opened and filled with test data - submit manually');
            }, 500);
        }

        // Event listeners for widget events
        window.addEventListener('feedbackWidget:submitted', function(e) {
            log(`✅ Widget submission successful: ${JSON.stringify(e.detail)}`);
            showResult('submission-result', 
                `✅ Widget submission successful!<br>` +
                `Your backend connection is working perfectly!`,
                'success'
            );
        });

        window.addEventListener('feedbackWidget:submitError', function(e) {
            log(`❌ Widget submission failed: ${e.detail.error}`);
            showResult('submission-result', 
                `❌ Widget submission failed!<br>` +
                `Error: ${e.detail.error}<br>` +
                `Check the detailed logs above for troubleshooting`,
                'error'
            );
        });

        // Initialize on page load
        detectEnvironment();
        log('Backend connection test page loaded');
        
        // Auto-run health check
        setTimeout(testBackendHealth, 1000);
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
Quick test script to verify backend and database connectivity.
Run this to diagnose any connection issues.
"""

import os
import sys
import requests
import json
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment():
    """Test environment variables."""
    print("🔍 Checking environment variables...")
    
    required_vars = ['SUPABASE_URL', 'SUPABASE_KEY', 'GROQ_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            # Show partial value for security
            masked_value = value[:10] + "..." + value[-5:] if len(value) > 15 else value
            print(f"  ✅ {var}: {masked_value}")
    
    if missing_vars:
        print(f"  ❌ Missing variables: {', '.join(missing_vars)}")
        return False
    
    return True

def test_supabase_connection():
    """Test direct Supabase connection."""
    print("\n🔍 Testing Supabase connection...")
    
    try:
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_KEY')
        supabase = create_client(supabase_url, supabase_key)
        
        # Test connection with a simple query
        result = supabase.table('comments').select("count", count="exact").execute()
        count = result.count if hasattr(result, 'count') else len(result.data)
        
        print(f"  ✅ Supabase connected successfully")
        print(f"  📊 Comments in database: {count}")
        
        # Test table structure
        try:
            result = supabase.table('comments').select("*").limit(1).execute()
            if result.data:
                columns = list(result.data[0].keys())
                print(f"  📋 Table columns: {', '.join(columns)}")
            else:
                print("  📋 Table is empty, testing insert...")
                test_insert_basic(supabase)
        except Exception as e:
            print(f"  ⚠️  Table structure issue: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Supabase connection failed: {e}")
        return False

def test_insert_basic(supabase):
    """Test basic insert functionality."""
    try:
        test_data = {
            'comment': 'Connection test comment',
            'company': 'Test Company',
            'name': 'Test User',
            'email': '<EMAIL>'
        }
        
        result = supabase.table('comments').insert(test_data).execute()
        
        if result.data:
            print(f"  ✅ Basic insert successful")
            # Clean up
            test_id = result.data[0]['id']
            supabase.table('comments').delete().eq('id', test_id).execute()
            print(f"  🧹 Cleaned up test data")
        
    except Exception as e:
        print(f"  ❌ Basic insert failed: {e}")

def test_backend_server():
    """Test if backend server is running."""
    print("\n🔍 Testing backend server...")
    
    # Test local server
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Local backend running: {data}")
            return test_backend_endpoint('http://localhost:5000')
        else:
            print(f"  ❌ Local backend returned status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("  ⚠️  Local backend not running (this is OK if testing production)")
    except Exception as e:
        print(f"  ❌ Local backend error: {e}")
    
    # Test production server
    try:
        response = requests.get('https://user-comments.onrender.com/', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Production backend running: {data}")
            return test_backend_endpoint('https://user-comments.onrender.com')
        else:
            print(f"  ❌ Production backend returned status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Production backend error: {e}")
    
    return False

def test_backend_endpoint(base_url):
    """Test the submit_comment endpoint."""
    print(f"\n🔍 Testing {base_url}/submit_comment...")
    
    test_data = {
        'comment': 'Backend connection test',
        'company': 'Test Company',
        'name': 'Test User',
        'email': '<EMAIL>',
        'website_url': 'https://test.com',
        'user_agent': 'Test Agent',
        'widget_version': '1.0.0'
    }
    
    try:
        response = requests.post(
            f'{base_url}/submit_comment',
            json=test_data,
            headers={
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-Widget-Version': '1.0.0',
                'X-Widget-Origin': 'test'
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ Endpoint test successful: {result}")
            return True
        else:
            print(f"  ❌ Endpoint returned status {response.status_code}")
            try:
                error_data = response.json()
                print(f"  📋 Error details: {error_data}")
            except:
                print(f"  📋 Response text: {response.text}")
            
    except Exception as e:
        print(f"  ❌ Endpoint test failed: {e}")
    
    return False

def main():
    """Run all tests."""
    print("🚀 Backend Connection Test")
    print("=" * 40)
    
    all_passed = True
    
    # Test environment
    if not test_environment():
        all_passed = False
    
    # Test Supabase
    if not test_supabase_connection():
        all_passed = False
    
    # Test backend server
    if not test_backend_server():
        all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 All tests passed! Your backend should be working.")
        print("\n📝 Next steps:")
        print("1. Start your backend: python backend/app.py")
        print("2. Test the widget in your browser")
    else:
        print("❌ Some tests failed. Check the errors above.")
        print("\n🔧 Common fixes:")
        print("1. Make sure .env file has correct values")
        print("2. Check Supabase project is active")
        print("3. Verify table structure in Supabase")
        print("4. Start backend server: python backend/app.py")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Database setup script for the feedback widget backend.
This script ensures your Supabase database has the correct table structure.
"""

import os
import sys
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_supabase_client():
    """Initialize and return Supabase client."""
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file")
        sys.exit(1)
    
    return create_client(supabase_url, supabase_key)

def check_table_exists(supabase: Client, table_name: str) -> bool:
    """Check if a table exists."""
    try:
        # Try to select from the table
        result = supabase.table(table_name).select("*").limit(1).execute()
        return True
    except Exception as e:
        print(f"Table '{table_name}' does not exist or is not accessible: {e}")
        return False

def check_column_exists(supabase: Client, table_name: str, column_name: str) -> bool:
    """Check if a column exists in a table."""
    try:
        # This is a simple check - try to select the column
        result = supabase.table(table_name).select(column_name).limit(1).execute()
        return True
    except Exception as e:
        return False

def create_comments_table(supabase: Client):
    """Create the comments table with full structure."""
    print("🔧 Creating comments table...")
    
    # Note: Supabase doesn't support direct DDL through the Python client
    # You'll need to run this SQL in your Supabase SQL editor
    sql = """
    -- Create comments table if it doesn't exist
    CREATE TABLE IF NOT EXISTS public.comments (
        id BIGSERIAL PRIMARY KEY,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        company TEXT,
        comment TEXT NOT NULL CHECK (LENGTH(TRIM(comment)) > 0),
        name TEXT,
        email TEXT,
        website_url TEXT,
        user_agent TEXT,
        widget_version VARCHAR(20),
        submission_ip INET,
        submission_metadata JSONB,
        is_sample BOOLEAN DEFAULT FALSE
    );

    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_comments_created_at ON public.comments (created_at);
    CREATE INDEX IF NOT EXISTS idx_comments_company ON public.comments (company);
    CREATE INDEX IF NOT EXISTS idx_comments_website_url ON public.comments (website_url);
    CREATE INDEX IF NOT EXISTS idx_comments_widget_version ON public.comments (widget_version);
    CREATE INDEX IF NOT EXISTS idx_comments_is_sample ON public.comments (is_sample);

    -- Enable Row Level Security (RLS)
    ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

    -- Create policies for public access (adjust as needed)
    CREATE POLICY IF NOT EXISTS "Allow public read access" ON public.comments
        FOR SELECT USING (true);

    CREATE POLICY IF NOT EXISTS "Allow public insert access" ON public.comments
        FOR INSERT WITH CHECK (true);

    -- Create analytics view
    CREATE OR REPLACE VIEW widget_analytics AS
    SELECT 
        DATE(created_at) as submission_date,
        website_url,
        widget_version,
        COUNT(*) as submission_count,
        COUNT(CASE WHEN name IS NOT NULL AND name != '' THEN 1 END) as submissions_with_name,
        COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as submissions_with_email,
        COUNT(CASE WHEN is_sample = true THEN 1 END) as sample_submissions
    FROM comments 
    GROUP BY DATE(created_at), website_url, widget_version
    ORDER BY submission_date DESC, submission_count DESC;
    """
    
    print("📋 SQL to run in Supabase SQL Editor:")
    print("=" * 60)
    print(sql)
    print("=" * 60)
    
    return sql

def test_database_connection(supabase: Client):
    """Test the database connection and table structure."""
    print("🔍 Testing database connection...")
    
    try:
        # Test basic connection
        result = supabase.table('comments').select("count", count="exact").execute()
        count = result.count if hasattr(result, 'count') else 0
        print(f"✅ Connected to database successfully")
        print(f"📊 Current comments count: {count}")
        
        # Test table structure
        print("\n🔍 Checking table structure...")
        
        # Try to select all columns to see what exists
        try:
            result = supabase.table('comments').select("*").limit(1).execute()
            if result.data:
                columns = list(result.data[0].keys())
                print(f"✅ Existing columns: {', '.join(columns)}")
            else:
                # Try to insert a test record to see what columns are required
                test_data = {
                    'comment': 'Database structure test',
                    'company': 'Test Company',
                    'name': 'Test User',
                    'email': '<EMAIL>',
                    'website_url': 'https://test.com',
                    'user_agent': 'Test Agent',
                    'widget_version': '1.0.0',
                    'is_sample': True
                }
                
                print("📝 Testing insert with enhanced data...")
                result = supabase.table('comments').insert(test_data).execute()
                
                if result.data:
                    print("✅ Enhanced table structure is working!")
                    # Clean up test data
                    test_id = result.data[0]['id']
                    supabase.table('comments').delete().eq('id', test_id).execute()
                    print("🧹 Cleaned up test data")
                
        except Exception as e:
            print(f"⚠️  Table structure issue: {e}")
            print("💡 You may need to run the migration SQL")
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    
    return True

def main():
    """Main setup function."""
    print("🚀 Setting up feedback widget database...")
    print("=" * 50)
    
    # Initialize Supabase client
    supabase = get_supabase_client()
    
    # Test connection
    if not test_database_connection(supabase):
        print("\n❌ Database setup failed!")
        sys.exit(1)
    
    # Check if table exists
    if not check_table_exists(supabase, 'comments'):
        print("\n📋 Comments table doesn't exist. Here's the SQL to create it:")
        create_comments_table(supabase)
        print("\n💡 Please run the SQL above in your Supabase SQL Editor")
        print("   Then run this script again to verify the setup")
    else:
        print("\n✅ Comments table exists!")
        
        # Check for enhanced columns
        enhanced_columns = ['website_url', 'user_agent', 'widget_version']
        missing_columns = []
        
        for column in enhanced_columns:
            if not check_column_exists(supabase, 'comments', column):
                missing_columns.append(column)
        
        if missing_columns:
            print(f"\n⚠️  Missing enhanced columns: {', '.join(missing_columns)}")
            print("📋 Run this SQL in Supabase to add them:")
            print("-" * 40)
            with open('migrations/002_enhance_comments_for_widget.sql', 'r') as f:
                print(f.read())
            print("-" * 40)
        else:
            print("✅ All enhanced columns are present!")
    
    print("\n🎉 Database setup check complete!")
    print("\n📝 Next steps:")
    print("1. If SQL was shown above, run it in your Supabase SQL Editor")
    print("2. Start your backend: python backend/app.py")
    print("3. Test the widget connection")

if __name__ == "__main__":
    main()

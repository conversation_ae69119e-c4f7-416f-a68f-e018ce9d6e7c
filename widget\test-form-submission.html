<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Submission Test - Feedback Widget</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 40px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .test-section {
            margin-bottom: 32px;
            padding: 24px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .test-btn {
            padding: 10px 16px;
            margin: 5px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .test-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .test-btn.primary {
            background: #00C2A8;
            color: white;
            border-color: #00C2A8;
        }
        
        .test-btn.primary:hover {
            background: #00a58e;
        }
        
        .test-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .test-btn.danger:hover {
            background: #dc2626;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: 500;
        }
        
        .status.success {
            background: #f0fdf4;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .status.info {
            background: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #bfdbfe;
        }
        
        .status.warning {
            background: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }
        
        #test-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 16px;
        }
        
        .validation-examples {
            background: #f8fafc;
            padding: 16px;
            border-radius: 6px;
            margin-top: 12px;
        }
        
        .validation-examples h5 {
            margin: 0 0 8px 0;
            color: #374151;
        }
        
        .validation-examples ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .validation-examples li {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 4px;
        }
        
        .auto-detect-info {
            background: #ecfdf5;
            border: 1px solid #bbf7d0;
            padding: 16px;
            border-radius: 8px;
            margin-top: 16px;
        }
        
        .auto-detect-info h4 {
            color: #166534;
            margin: 0 0 8px 0;
        }
        
        .auto-detect-info code {
            background: #f0fdf4;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📝 Form Submission Test</h1>
        <p>Test comprehensive form validation, AJAX submission, rate limiting, and auto-detection features.</p>
        
        <div class="test-section">
            <h3>Auto-Detection Test</h3>
            <p>The widget automatically detects website information:</p>
            <button class="test-btn primary" onclick="testAutoDetection()">🔍 Test Auto-Detection</button>
            <div id="auto-detect-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Form Validation Tests</h3>
            <p>Test different validation scenarios:</p>
            <div class="test-grid">
                <div class="test-card">
                    <h4>Comment Validation</h4>
                    <button class="test-btn" onclick="testEmptyComment()">❌ Empty Comment</button>
                    <button class="test-btn" onclick="testShortComment()">📏 Too Short</button>
                    <button class="test-btn" onclick="testLongComment()">📏 Too Long</button>
                    <button class="test-btn" onclick="testSpamComment()">🚫 Spam Pattern</button>
                    <div class="validation-examples">
                        <h5>Test Cases:</h5>
                        <ul>
                            <li>Empty: "" (should fail)</li>
                            <li>Too short: "Hi" (should fail)</li>
                            <li>Too long: 2000+ chars (should fail)</li>
                            <li>Spam: "AAAAAAAAAAAAA" (should fail)</li>
                        </ul>
                    </div>
                </div>
                
                <div class="test-card">
                    <h4>Email Validation</h4>
                    <button class="test-btn" onclick="testValidEmail()">✅ Valid Email</button>
                    <button class="test-btn" onclick="testInvalidEmail()">❌ Invalid Email</button>
                    <button class="test-btn" onclick="testEmptyEmail()">⭕ Empty Email</button>
                    <div class="validation-examples">
                        <h5>Test Cases:</h5>
                        <ul>
                            <li>Valid: "<EMAIL>" (should pass)</li>
                            <li>Invalid: "invalid-email" (should fail)</li>
                            <li>Empty: "" (should pass - optional)</li>
                        </ul>
                    </div>
                </div>
                
                <div class="test-card">
                    <h4>Name Validation</h4>
                    <button class="test-btn" onclick="testValidName()">✅ Valid Name</button>
                    <button class="test-btn" onclick="testInvalidName()">❌ Invalid Name</button>
                    <button class="test-btn" onclick="testLongName()">📏 Too Long Name</button>
                    <div class="validation-examples">
                        <h5>Test Cases:</h5>
                        <ul>
                            <li>Valid: "John Doe" (should pass)</li>
                            <li>Invalid: "John123" (should fail)</li>
                            <li>Too long: 100+ chars (should fail)</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div id="validation-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Rate Limiting Tests</h3>
            <p>Test client-side rate limiting:</p>
            <button class="test-btn danger" onclick="testRapidSubmissions()">⚡ Rapid Submissions</button>
            <button class="test-btn" onclick="testHourlyLimit()">⏰ Hourly Limit</button>
            <button class="test-btn" onclick="resetRateLimit()">🔄 Reset Rate Limit</button>
            <div class="status warning">
                <strong>Rate Limits:</strong><br>
                • 2 second cooldown between submissions<br>
                • Maximum 5 submissions per hour<br>
                • Automatic reset after 1 hour
            </div>
            <div id="rate-limit-result"></div>
        </div>
        
        <div class="test-section">
            <h3>AJAX Submission Test</h3>
            <p>Test actual form submission to the backend:</p>
            <button class="test-btn primary" onclick="testRealSubmission()">📤 Test Real Submission</button>
            <button class="test-btn" onclick="testNetworkError()">🌐 Simulate Network Error</button>
            <button class="test-btn" onclick="testTimeout()">⏱️ Simulate Timeout</button>
            <div id="submission-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Form Reset Test</h3>
            <p>Test form reset after successful submission:</p>
            <button class="test-btn" onclick="testFormReset()">🔄 Test Form Reset</button>
            <div id="reset-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Test Log</h3>
            <button class="test-btn" onclick="clearLog()">🗑️ Clear Log</button>
            <div id="test-log"></div>
        </div>
    </div>

    <!-- Load the feedback widget -->
    <script src="widget.js"></script>
    <script>
        // Initialize the widget
        FeedbackWidget.init({
            position: 'bottom-right',
            primaryColor: '#00C2A8',
            title: 'Form Test',
            placeholder: 'Test form submission...',
            showName: true,
            showEmail: true
        });

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[TEST] ${message}`);
        }

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        // Auto-detection tests
        function testAutoDetection() {
            log('Testing auto-detection...');
            const url = window.location.href;
            const domain = window.location.hostname;
            
            showResult('auto-detect-result', `
                <div class="auto-detect-info">
                    <h4>Auto-Detected Information:</h4>
                    <p><strong>Website URL:</strong> <code>${url}</code></p>
                    <p><strong>Domain:</strong> <code>${domain}</code></p>
                    <p><strong>Protocol:</strong> <code>${window.location.protocol}</code></p>
                    <p><strong>Port:</strong> <code>${window.location.port || 'default'}</code></p>
                </div>
            `, 'success');
            
            log(`Auto-detection: URL=${url}, Domain=${domain}`);
        }

        // Validation tests
        function fillFormAndSubmit(comment, name = '', email = '') {
            FeedbackWidget.open();
            
            setTimeout(() => {
                const commentField = document.getElementById('feedback-widget-comment');
                const nameField = document.getElementById('feedback-widget-name');
                const emailField = document.getElementById('feedback-widget-email');
                
                if (commentField) commentField.value = comment;
                if (nameField) nameField.value = name;
                if (emailField) emailField.value = email;
                
                // Trigger form submission
                const form = document.getElementById('feedback-widget-form');
                if (form) {
                    const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                    form.dispatchEvent(submitEvent);
                }
            }, 500);
        }

        function testEmptyComment() {
            log('Testing empty comment validation...');
            fillFormAndSubmit('', 'Test User', '<EMAIL>');
            showResult('validation-result', 'Empty comment test triggered - check widget for validation error', 'info');
        }

        function testShortComment() {
            log('Testing short comment validation...');
            fillFormAndSubmit('Hi', 'Test User', '<EMAIL>');
            showResult('validation-result', 'Short comment test triggered - check widget for validation error', 'info');
        }

        function testLongComment() {
            log('Testing long comment validation...');
            const longComment = 'A'.repeat(2001);
            fillFormAndSubmit(longComment, 'Test User', '<EMAIL>');
            showResult('validation-result', 'Long comment test triggered - check widget for validation error', 'info');
        }

        function testSpamComment() {
            log('Testing spam comment validation...');
            fillFormAndSubmit('AAAAAAAAAAAAAAAAAAAAAA', 'Test User', '<EMAIL>');
            showResult('validation-result', 'Spam comment test triggered - check widget for validation error', 'info');
        }

        function testValidEmail() {
            log('Testing valid email...');
            fillFormAndSubmit('This is a valid test comment', 'Test User', '<EMAIL>');
            showResult('validation-result', 'Valid email test triggered - should pass validation', 'success');
        }

        function testInvalidEmail() {
            log('Testing invalid email...');
            fillFormAndSubmit('This is a test comment', 'Test User', 'invalid-email');
            showResult('validation-result', 'Invalid email test triggered - check widget for validation error', 'info');
        }

        function testEmptyEmail() {
            log('Testing empty email (should be valid)...');
            fillFormAndSubmit('This is a test comment', 'Test User', '');
            showResult('validation-result', 'Empty email test triggered - should pass (optional field)', 'success');
        }

        function testValidName() {
            log('Testing valid name...');
            fillFormAndSubmit('This is a test comment', 'John Doe', '<EMAIL>');
            showResult('validation-result', 'Valid name test triggered - should pass validation', 'success');
        }

        function testInvalidName() {
            log('Testing invalid name...');
            fillFormAndSubmit('This is a test comment', 'John123', '<EMAIL>');
            showResult('validation-result', 'Invalid name test triggered - check widget for validation error', 'info');
        }

        function testLongName() {
            log('Testing long name...');
            const longName = 'A'.repeat(101);
            fillFormAndSubmit('This is a test comment', longName, '<EMAIL>');
            showResult('validation-result', 'Long name test triggered - check widget for validation error', 'info');
        }

        // Rate limiting tests
        function testRapidSubmissions() {
            log('Testing rapid submissions...');
            showResult('rate-limit-result', 'Rapid submission test - try submitting multiple times quickly', 'warning');
            
            // Submit first form
            fillFormAndSubmit('First rapid submission', 'Test User', '<EMAIL>');
            
            // Try to submit again immediately
            setTimeout(() => {
                fillFormAndSubmit('Second rapid submission', 'Test User', '<EMAIL>');
            }, 100);
        }

        function testHourlyLimit() {
            log('Testing hourly limit (simulated)...');
            showResult('rate-limit-result', 'To test hourly limit, submit 5 valid forms within an hour', 'info');
        }

        function resetRateLimit() {
            log('Resetting rate limit (for testing)...');
            // This would need to be implemented in the widget for testing
            showResult('rate-limit-result', 'Rate limit reset (refresh page for full reset)', 'success');
        }

        // Submission tests
        function testRealSubmission() {
            log('Testing real submission to backend...');
            fillFormAndSubmit('This is a real test submission from the form test page', 'Test User', '<EMAIL>');
            showResult('submission-result', 'Real submission test triggered - check network tab and widget response', 'info');
        }

        function testNetworkError() {
            log('Network error simulation not implemented in this test');
            showResult('submission-result', 'Network error test would require backend modification', 'warning');
        }

        function testTimeout() {
            log('Timeout simulation not implemented in this test');
            showResult('submission-result', 'Timeout test would require backend modification', 'warning');
        }

        // Form reset test
        function testFormReset() {
            log('Testing form reset...');
            FeedbackWidget.open();
            
            setTimeout(() => {
                const commentField = document.getElementById('feedback-widget-comment');
                const nameField = document.getElementById('feedback-widget-name');
                const emailField = document.getElementById('feedback-widget-email');
                
                if (commentField) commentField.value = 'Test content to be reset';
                if (nameField) nameField.value = 'Test Name';
                if (emailField) emailField.value = '<EMAIL>';
                
                log('Form filled with test data');
                showResult('reset-result', 'Form filled with test data. Now close and reopen to test reset.', 'info');
            }, 500);
        }

        // Event listeners for widget events
        window.addEventListener('feedbackWidget:submitted', function(e) {
            log(`Submission successful: ${JSON.stringify(e.detail)}`);
            showResult('submission-result', 'Form submitted successfully! Check console for details.', 'success');
        });

        window.addEventListener('feedbackWidget:submitError', function(e) {
            log(`Submission error: ${e.detail.error}`);
            showResult('submission-result', `Submission failed: ${e.detail.error}`, 'error');
        });

        // Initial log
        log('Form submission test page loaded');
        testAutoDetection();
    </script>
</body>
</html>

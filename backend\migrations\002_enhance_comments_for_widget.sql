-- Enhanced comments table structure for feedback widget
-- This migration adds columns to support the enhanced widget data

-- Add new columns for widget metadata
ALTER TABLE comments 
ADD COLUMN IF NOT EXISTS website_url TEXT,
ADD COLUMN IF NOT EXISTS user_agent TEXT,
ADD COLUMN IF NOT EXISTS widget_version VARCHAR(20),
ADD COLUMN IF NOT EXISTS submission_ip INET,
ADD COLUMN IF NOT EXISTS submission_metadata JSONB;

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_comments_website_url ON public.comments (website_url);
CREATE INDEX IF NOT EXISTS idx_comments_widget_version ON public.comments (widget_version);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON public.comments (created_at);

-- Add comments to document the new columns
COMMENT ON COLUMN comments.website_url IS 'The full URL of the website where the widget was embedded';
COMMENT ON COLUMN comments.user_agent IS 'Browser user agent string for analytics';
COMMENT ON COLUMN comments.widget_version IS 'Version of the feedback widget used';
COMMENT ON COLUMN comments.submission_ip IS 'IP address of the submitter (optional, for spam prevention)';
COMMENT ON COLUMN comments.submission_metadata IS 'Additional metadata in JSON format';

-- Ensure the table has proper constraints
ALTER TABLE comments 
ALTER COLUMN comment SET NOT NULL,
ALTER COLUMN created_at SET DEFAULT NOW();

-- Add a check constraint to ensure comment is not empty
ALTER TABLE comments 
ADD CONSTRAINT IF NOT EXISTS check_comment_not_empty 
CHECK (LENGTH(TRIM(comment)) > 0);

-- Create a view for analytics (optional)
CREATE OR REPLACE VIEW widget_analytics AS
SELECT 
    DATE(created_at) as submission_date,
    website_url,
    widget_version,
    COUNT(*) as submission_count,
    COUNT(CASE WHEN name IS NOT NULL AND name != '' THEN 1 END) as submissions_with_name,
    COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as submissions_with_email,
    COUNT(CASE WHEN is_sample = true THEN 1 END) as sample_submissions
FROM comments 
GROUP BY DATE(created_at), website_url, widget_version
ORDER BY submission_date DESC, submission_count DESC;

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT ON widget_analytics TO anon;
-- GRANT SELECT ON widget_analytics TO authenticated;

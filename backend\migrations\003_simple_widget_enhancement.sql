-- Simple and safe enhancement for feedback widget
-- This script adds the essential columns needed for the widget

-- Add new columns for widget metadata (safe approach)
ALTER TABLE comments 
ADD COLUMN IF NOT EXISTS website_url TEXT,
ADD COLUMN IF NOT EXISTS user_agent TEXT,
ADD COLUMN IF NOT EXISTS widget_version VARCHAR(20);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_comments_website_url ON public.comments (website_url);
CREATE INDEX IF NOT EXISTS idx_comments_widget_version ON public.comments (widget_version);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON public.comments (created_at);

-- Add column comments for documentation
COMMENT ON COLUMN comments.website_url IS 'The full URL of the website where the widget was embedded';
COMMENT ON COLUMN comments.user_agent IS 'Browser user agent string for analytics';
COMMENT ON COLUMN comments.widget_version IS 'Version of the feedback widget used';

-- Ensure created_at has a default (safe approach)
ALTER TABLE comments ALTER COLUMN created_at SET DEFAULT NOW();

-- Make comment column NOT NULL (safe approach)
UPDATE comments SET comment = 'No comment provided' WHERE comment IS NULL OR TRIM(comment) = '';
ALTER TABLE comments ALTER COLUMN comment SET NOT NULL;

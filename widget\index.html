<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Widget - Live Demo & Integration Guide</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 40px;
            background: #f8fafc;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .subtitle {
            color: #6b7280;
            margin-bottom: 32px;
            font-size: 18px;
        }
        
        .demo-section {
            margin-bottom: 32px;
            padding: 24px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .demo-section h2 {
            color: #374151;
            margin-bottom: 16px;
            font-size: 20px;
        }
        
        .demo-section p {
            color: #6b7280;
            margin-bottom: 16px;
        }
        
        .demo-controls {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .demo-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .demo-btn.primary {
            background: #00C2A8;
            color: white;
            border-color: #00C2A8;
        }
        
        .demo-btn.primary:hover {
            background: #00a58e;
            border-color: #00a58e;
        }
        
        .config-section {
            background: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .config-row {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .config-row label {
            min-width: 120px;
            font-weight: 500;
            color: #374151;
        }
        
        .config-row select,
        .config-row input {
            padding: 6px 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin-top: 16px;
        }
        
        .highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Feedback Widget Demo</h1>
        <p class="subtitle">Test the embeddable feedback widget with different configurations</p>
        
        <div class="demo-section">
            <h2>Widget Controls</h2>
            <p>Use these buttons to test the widget functionality:</p>
            <div class="demo-controls">
                <button class="demo-btn primary" onclick="FeedbackWidget.toggle()">Toggle Widget</button>
                <button class="demo-btn" onclick="FeedbackWidget.open()">Open Widget</button>
                <button class="demo-btn" onclick="FeedbackWidget.close()">Close Widget</button>
                <button class="demo-btn" onclick="reinitializeWidget()">Reinitialize</button>
                <button class="demo-btn" onclick="checkWidgetState()">Check State</button>
                <button class="demo-btn" onclick="openResponsiveTest()">📱 Responsive Test</button>
                <button class="demo-btn" onclick="openFormSubmissionTest()">📝 Form Test</button>
                <button class="demo-btn" onclick="openValidationTest()">🔒 Validation Test</button>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>Configuration Options</h2>
            <p>Customize the widget appearance and behavior:</p>
            
            <div class="config-section">
                <div class="config-row">
                    <label>Position:</label>
                    <select id="position">
                        <option value="bottom-right">Bottom Right</option>
                        <option value="bottom-left">Bottom Left</option>
                        <option value="top-right">Top Right</option>
                        <option value="top-left">Top Left</option>
                    </select>
                </div>
                
                <div class="config-row">
                    <label>Primary Color:</label>
                    <input type="color" id="primaryColor" value="#00C2A8">
                </div>
                
                <div class="config-row">
                    <label>Title:</label>
                    <input type="text" id="title" value="Feedback" placeholder="Widget title">
                </div>
                
                <div class="config-row">
                    <label>Placeholder:</label>
                    <input type="text" id="placeholder" value="Share your feedback..." placeholder="Comment placeholder">
                </div>
                
                <div class="config-row">
                    <label>Show Name Field:</label>
                    <input type="checkbox" id="showName" checked>
                </div>
                
                <div class="config-row">
                    <label>Show Email Field:</label>
                    <input type="checkbox" id="showEmail" checked>
                </div>
                
                <button class="demo-btn primary" onclick="updateWidget()">Apply Changes</button>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>Integration Code</h2>
            <p>Copy this code to embed the widget on any website:</p>
            
            <div class="code-block">
&lt;!-- Add this before closing &lt;/body&gt; tag --&gt;<br>
&lt;script src="<span class="highlight">https://your-project-name.vercel.app/widget.js</span>"&gt;&lt;/script&gt;<br>
&lt;script&gt;<br>
&nbsp;&nbsp;FeedbackWidget.init({<br>
&nbsp;&nbsp;&nbsp;&nbsp;position: '<span id="code-position">bottom-right</span>',<br>
&nbsp;&nbsp;&nbsp;&nbsp;primaryColor: '<span id="code-color">#00C2A8</span>',<br>
&nbsp;&nbsp;&nbsp;&nbsp;title: '<span id="code-title">Feedback</span>',<br>
&nbsp;&nbsp;&nbsp;&nbsp;placeholder: '<span id="code-placeholder">Share your feedback...</span>',<br>
&nbsp;&nbsp;&nbsp;&nbsp;showName: <span id="code-name">true</span>,<br>
&nbsp;&nbsp;&nbsp;&nbsp;showEmail: <span id="code-email">true</span><br>
&nbsp;&nbsp;});<br>
&lt;/script&gt;
            </div>
        </div>
        
        <div class="demo-section">
            <h2>Features Implemented</h2>
            <ul style="color: #374151; line-height: 1.8;">
                <li>✅ Self-contained JavaScript widget with namespace isolation</li>
                <li>✅ Floating feedback button with customizable position</li>
                <li>✅ Slide-out feedback form panel with smooth animations</li>
                <li>✅ Form fields: Comment (required), Name (optional), Email (optional)</li>
                <li>✅ Auto-detection of website domain</li>
                <li>✅ Responsive design for mobile and desktop</li>
                <li>✅ Dark theme support</li>
                <li>✅ <strong>Enhanced keyboard accessibility (ESC to close, focus trapping)</strong></li>
                <li>✅ <strong>Improved click outside to close functionality</strong></li>
                <li>✅ <strong>Body scroll prevention when widget is open</strong></li>
                <li>✅ <strong>Smooth toggle animations with cubic-bezier easing</strong></li>
                <li>✅ <strong>Toggle functionality with click and keyboard support</strong></li>
                <li>✅ <strong>Mobile-first responsive design with breakpoint optimization</strong></li>
                <li>✅ <strong>Enhanced z-index management (max safe z-index: 2147483647)</strong></li>
                <li>✅ <strong>Comprehensive CSS reset to prevent host site interference</strong></li>
                <li>✅ <strong>Automatic dark/light theme detection and adaptation</strong></li>
                <li>✅ <strong>High contrast and reduced motion accessibility support</strong></li>
                <li>✅ <strong>ARIA attributes for screen readers</strong></li>
                <li>✅ <strong>Focus management and restoration</strong></li>
                <li>✅ <strong>Comprehensive form validation (required fields, email format, length limits, spam detection)</strong></li>
                <li>✅ <strong>Honeypot field for advanced spam bot prevention</strong></li>
                <li>✅ <strong>Real-time validation feedback (blur/input events)</strong></li>
                <li>✅ <strong>AJAX submission to backend with timeout and error handling</strong></li>
                <li>✅ <strong>Auto-detection of website URL and domain</strong></li>
                <li>✅ <strong>Client-side rate limiting (2s cooldown, 5/hour limit)</strong></li>
                <li>✅ <strong>Automatic form reset after successful submission</strong></li>
                <li>✅ <strong>Custom events (feedbackWidget:opened, feedbackWidget:closed, feedbackWidget:themeChanged, feedbackWidget:submitted, feedbackWidget:submitError)</strong></li>
                <li>✅ Loading states and success/error messages</li>
                <li>✅ CSS reset to prevent host site style conflicts</li>
                <li>✅ Customizable colors, position, and text</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>Responsive Design Features</h2>
            <ul style="color: #374151; line-height: 1.8;">
                <li>📱 <strong>Mobile-First Approach:</strong> Optimized for mobile devices with progressive enhancement</li>
                <li>🎯 <strong>Touch-Friendly Targets:</strong> Minimum 48px touch targets on mobile devices</li>
                <li>📐 <strong>Adaptive Sizing:</strong> Button and panel sizes adapt to screen size</li>
                <li>🔄 <strong>Smart Positioning:</strong> Bottom-center on mobile, configurable on desktop</li>
                <li>🌙 <strong>Theme Detection:</strong> Automatic dark/light theme adaptation</li>
                <li>♿ <strong>Accessibility Support:</strong> High contrast and reduced motion preferences</li>
                <li>🛡️ <strong>CSS Isolation:</strong> Comprehensive reset prevents host site interference</li>
                <li>⚡ <strong>Maximum Z-Index:</strong> Ensures widget appears above all content</li>
            </ul>
            <div style="margin-top: 16px;">
                <button class="demo-btn primary" onclick="openResponsiveTest()">📱 Open Responsive Test Page</button>
            </div>
        </div>

        <div class="demo-section">
            <h2>Form Submission Features</h2>
            <ul style="color: #374151; line-height: 1.8;">
                <li>✅ <strong>Comprehensive Validation:</strong> Required fields, email format, length limits, spam detection</li>
                <li>🚀 <strong>AJAX Submission:</strong> Asynchronous submission with 30-second timeout</li>
                <li>🌐 <strong>Auto-Detection:</strong> Automatically captures website URL and domain</li>
                <li>⏱️ <strong>Rate Limiting:</strong> 2-second cooldown, maximum 5 submissions per hour</li>
                <li>🔄 <strong>Smart Reset:</strong> Form clears after successful submission</li>
                <li>🛡️ <strong>Error Handling:</strong> Network errors, timeouts, server errors</li>
                <li>📊 <strong>Event System:</strong> Custom events for submission success/failure</li>
                <li>🎯 <strong>Field Validation:</strong> Real-time validation with inline error messages</li>
            </ul>
            <div style="margin-top: 16px;">
                <button class="demo-btn primary" onclick="openFormSubmissionTest()">📝 Open Form Submission Test Page</button>
            </div>
        </div>

        <div class="demo-section">
            <h2>Accessibility Features</h2>
            <ul style="color: #374151; line-height: 1.8;">
                <li>🔍 <strong>Screen Reader Support:</strong> ARIA labels, roles, and properties</li>
                <li>⌨️ <strong>Keyboard Navigation:</strong> Tab, Shift+Tab, and ESC key support</li>
                <li>🎯 <strong>Focus Management:</strong> Focus trapping within widget when open</li>
                <li>🔄 <strong>Focus Restoration:</strong> Returns focus to trigger element when closed</li>
                <li>🚫 <strong>Scroll Prevention:</strong> Prevents background scrolling when widget is open</li>
                <li>📱 <strong>Mobile Friendly:</strong> Touch-friendly targets and responsive design</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>Environment Info</h2>
            <p>Current environment and API configuration:</p>
            <div id="environment-info" style="background: #f3f4f6; padding: 12px; border-radius: 6px; font-family: monospace; font-size: 12px; margin-bottom: 16px;">
                <div style="color: #6b7280;">Environment info will appear here...</div>
            </div>
        </div>

        <div class="demo-section">
            <h2>Event Monitoring</h2>
            <p>The widget dispatches custom events that you can listen to:</p>
            <div id="event-log" style="background: #f3f4f6; padding: 12px; border-radius: 6px; font-family: monospace; font-size: 12px; max-height: 150px; overflow-y: auto;">
                <div style="color: #6b7280;">Event log will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Load the feedback widget -->
    <script src="widget.js"></script>
    <script>
        // Initialize the widget with default settings
        FeedbackWidget.init({
            position: 'bottom-right',
            primaryColor: '#00C2A8',
            title: 'Feedback',
            placeholder: 'Share your feedback...',
            showName: true,
            showEmail: true
        });

        // Display environment information
        function displayEnvironmentInfo() {
            const hostname = window.location.hostname;
            const isLocal = hostname === 'localhost' ||
                           hostname === '127.0.0.1' ||
                           hostname === '' ||
                           window.location.protocol === 'file:';

            const apiUrl = isLocal ?
                'http://localhost:5000/submit_comment' :
                'https://user-comments.onrender.com/submit_comment';

            const envInfo = document.getElementById('environment-info');
            envInfo.innerHTML = `
                <div style="color: ${isLocal ? '#059669' : '#dc2626'}; font-weight: bold;">
                    🌍 Environment: ${isLocal ? 'LOCAL DEVELOPMENT' : 'PRODUCTION'}
                </div>
                <div style="color: #374151; margin-top: 4px;">
                    📍 Hostname: ${hostname || 'file://'}
                </div>
                <div style="color: #374151; margin-top: 4px;">
                    🔗 API URL: ${apiUrl}
                </div>
                <div style="color: #374151; margin-top: 4px;">
                    🌐 Origin: ${window.location.origin || 'file://'}
                </div>
            `;
        }

        // Call it after widget initialization
        displayEnvironmentInfo();

        // Demo functions
        function updateWidget() {
            const position = document.getElementById('position').value;
            const primaryColor = document.getElementById('primaryColor').value;
            const title = document.getElementById('title').value;
            const placeholder = document.getElementById('placeholder').value;
            const showName = document.getElementById('showName').checked;
            const showEmail = document.getElementById('showEmail').checked;

            // Remove existing widget
            const existingContainer = document.getElementById('feedback-widget-container');
            if (existingContainer) {
                existingContainer.remove();
            }

            // Remove existing styles
            const existingStyles = document.getElementById('feedback-widget-styles');
            if (existingStyles) {
                existingStyles.remove();
            }

            // Reinitialize with new config
            FeedbackWidget.init({
                position,
                primaryColor,
                title,
                placeholder,
                showName,
                showEmail
            });

            // Update code example
            updateCodeExample();
        }

        function reinitializeWidget() {
            updateWidget();
        }

        function checkWidgetState() {
            const isOpen = FeedbackWidget.isOpen();
            alert(`Widget is currently: ${isOpen ? 'OPEN' : 'CLOSED'}`);
        }

        function openResponsiveTest() {
            window.open('test-responsive.html', '_blank');
        }

        function openFormSubmissionTest() {
            window.open('test-form-submission.html', '_blank');
        }

        function openValidationTest() {
            window.open('test-validation-enhancements.html', '_blank');
        }

        function updateCodeExample() {
            document.getElementById('code-position').textContent = document.getElementById('position').value;
            document.getElementById('code-color').textContent = document.getElementById('primaryColor').value;
            document.getElementById('code-title').textContent = document.getElementById('title').value;
            document.getElementById('code-placeholder').textContent = document.getElementById('placeholder').value;
            document.getElementById('code-name').textContent = document.getElementById('showName').checked;
            document.getElementById('code-email').textContent = document.getElementById('showEmail').checked;
        }

        // Initialize code example
        updateCodeExample();

        // Set up event monitoring
        setupEventMonitoring();

        function setupEventMonitoring() {
            const eventLog = document.getElementById('event-log');

            function logEvent(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.style.color = '#374151';
                logEntry.innerHTML = `<span style="color: #6b7280;">[${timestamp}]</span> ${message}`;

                // Clear placeholder text
                if (eventLog.children.length === 1 && eventLog.children[0].textContent.includes('Event log will appear here')) {
                    eventLog.innerHTML = '';
                }

                eventLog.appendChild(logEntry);
                eventLog.scrollTop = eventLog.scrollHeight;

                // Keep only last 10 entries
                while (eventLog.children.length > 10) {
                    eventLog.removeChild(eventLog.firstChild);
                }
            }

            // Listen for widget events
            window.addEventListener('feedbackWidget:opened', function(e) {
                logEvent('🟢 Widget opened');
                console.log('Feedback widget opened:', e.detail);
            });

            window.addEventListener('feedbackWidget:closed', function(e) {
                logEvent('🔴 Widget closed');
                console.log('Feedback widget closed:', e.detail);
            });

            // Log initial state
            logEvent('📡 Event monitoring started');
        }
    </script>
</body>
</html>
